# -*- coding: utf-8 -*-

from datetime import datetime, timedelta

from odoo import api, fields, models, Command, _
from odoo.exceptions import UserError


class SaleOrderLineExtend(models.Model):
    """
    This class extends the sale.order.line model to add price list related functionality
    """
    _inherit = 'sale.order.line'

    suggested_price = fields.Float(
        string='Suggested Price',
        compute='_compute_suggested_price',
        store=True,
        readonly=False,
        help="The suggested price for the product based on the current price list."
    )

    price_difference = fields.Float(
        string='Price Difference',
        compute='_compute_price_difference',
        store=True,
        help="Difference between the suggested price and the actual price."
    )

    has_pricelist_item = fields.Boolean(
        string='Has Price List Item',
        compute='_compute_suggested_price',
        store=True,
        help="Indicates if the product has a price list item configured."
    )

    # 新增字段：控制价格同步按钮显示
    show_price_sync_btn = fields.<PERSON><PERSON>an(
        string="显示价格同步按钮",
        compute='_compute_show_price_sync_btn',
    )

    # 新增字段：价格同步按钮的提示文本
    price_sync_tooltip = fields.Char(
        string="价格同步提示",
        compute='_compute_show_price_sync_btn',
    )

    # history_invisible = fields.Boolean(
    #     string='History Invisible',
    #     # default="_get_default_history_invisible",
    #     help="This field is used to hide the history of this field in the form view."
    # )

    global_pricelist_count = fields.Integer(
        string='Global Pricelist Count',
        compute='_compute_global_pricelist_count',
        help="The count of global pricelists that have this product as a price list item."
    )

    @api.onchange('product_id')
    def copy_product_template_name(self):
        for line in self:
            tmpl_name = line.product_id.product_tmpl_id.name or ''
            if tmpl_name:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'ylch_copy_to_clipboard',
                    'params': {
                        'text': tmpl_name,
                    }
                }

    @api.onchange('product_id', 'order_id.pricelist_id')
    def _compute_global_pricelist_count(self):
        """计算当前产品在全局定价规则中的数量"""
        for line in self:
            # 只查当前订单行相关的定价规则
            pricelist_items = self.env['product.pricelist.item'].search_count([
                ('pricelist_id.owner_id', '=', False),
                '|',
                ('product_id', '=', line.product_id.id),
                ('product_tmpl_id', '=', line.product_id.product_tmpl_id.id),
            ])
            line.global_pricelist_count = pricelist_items

    # @api.onchange('product_id')
    # def _get_default_history_invisible(self):
    #     """如果价格表内没有这个产品，则显示历史记录按钮"""
    #     if not self.order_id.pricelist_id:
    #         self.history_invisible = False
    #         return
    #
    #     pricelist_items = self.order_id.pricelist_id.item_ids
    #     # 直接提取 product_id 和 product_tmpl_id
    #     product_ids = pricelist_items.filtered(lambda item: item.product_id).mapped('product_id.id')
    #     product_tmpl_ids = pricelist_items.filtered(lambda item: item.product_tmpl_id).mapped('product_tmpl_id.id')
    #
    #     # 合并为一个列表
    #     pricelist_product_ids = set(product_ids + product_tmpl_ids)
    #
    #     # 判断当前产品是否在价格表内
    #     self.history_invisible = self.product_id.id in pricelist_product_ids

    def get_all_global_pricelists(self):
        """获取当前订单行的全局定价规则，并合并变体和模板价格"""

        # 检查是否有 skip_save 上下文
        if not self.product_id and self.env.context.get('skip_save'):
            # 从上下文获取产品信息
            product_id = self.env.context.get('product_id')
            
            if not product_id:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': '错误',
                        'message': '缺少产品信息',
                        'type': 'danger',
                        'sticky': False,
                    }
                }
            
            product = self.env['product.product'].browse(product_id)
            return self._get_global_pricelists_for_product(product)
        else:
            # 原有逻辑：已保存的记录
            self.ensure_one()
            if not self.product_id:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': '错误',
                        'message': '缺少产品信息',
                        'type': 'danger',
                        'sticky': False,
                    }
                }
            
            return self._get_global_pricelists_for_product(self.product_id)

    def _get_global_pricelists_for_product(self, product):
        """为指定产品获取全局价格表"""
        # 只查当前订单行相关的定价规则
        pricelist_items = self.env['product.pricelist.item'].search([
            ('pricelist_id.owner_id', '=', False),
            '|',
            ('product_id', '=', product.id),
            ('product_tmpl_id', '=', product.product_tmpl_id.id),
        ])
        
        if not pricelist_items:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': '信息',
                    'message': '未找到全局价格表项',
                    'type': 'info',
                    'sticky': False,
                }
            }
        
        # 初始化价格数据
        price_data = []

        for item in pricelist_items:
            price_type = 'variant' if item.product_id else 'template'
            price_data.append({
                'name': item.pricelist_id.display_name,
                'product_id': product.id,
                'price_unit': item.fixed_price,
                'sale_order_line_id': self.id if self.id else False,
                'min_quantity': item.min_quantity,
                'price_type': price_type,
            })

        # 生成向导记录（只有一个产品）
        wizards = self.env['global.pricelist.wizard'].create(price_data)

        return {
            'name': '获取全局价格表',
            'type': 'ir.actions.act_window',
            'res_model': 'global.pricelist.wizard',
            'view_mode': 'list',
            'target': 'new',
            'domain': [('id', 'in', wizards.ids)],
        }

    def action_sale_order_history_price(self):
        """查看历史销售价格"""
        # 检查是否有 skip_save 上下文
        if not self.product_id and self.env.context.get('skip_save'):
            # 从上下文获取产品和客户信息
            product_id = self.env.context.get('product_id')
            partner_id = self.env.context.get('partner_id')
            
            if not product_id:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': '错误',
                        'message': '缺少产品信息',
                        'type': 'danger',
                        'sticky': False,
                    }
                }
            
            if not partner_id:
                # 尝试从订单获取客户信息
                order_id = self.env.context.get('order_id')
                if order_id:
                    order = self.env['sale.order'].browse(order_id)
                    if order.partner_id:
                        partner_id = order.partner_id.id
            
            if not partner_id:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': '错误',
                        'message': '缺少客户信息',
                        'type': 'danger',
                        'sticky': False,
                    }
                }
            
            product = self.env['product.product'].browse(product_id)
            partner = self.env['res.partner'].browse(partner_id)
            
            return self._get_history_price_for_product(product, partner)
        else:
            self.ensure_one()
            if not self.product_id or not self.order_id.partner_id:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': '错误',
                        'message': '缺少产品或客户信息',
                        'type': 'danger',
                        'sticky': False,
                    }
                }
            
            return self._get_history_price_for_product(self.product_id, self.order_id.partner_id)

    def _get_history_price_for_product(self, product, partner):
        """为指定产品和客户获取历史价格"""
        categ = product.product_tmpl_id.categ_id

        def get_history_price_day(categ):
            if categ.history_price_day:
                return categ.history_price_day
            if categ.parent_id:
                return get_history_price_day(categ.parent_id)
            return None

        days = get_history_price_day(categ)
        if not days:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': '信息',
                    'message': '产品类别未配置历史价格天数',
                    'type': 'info',
                    'sticky': False,
                }
            }

        start_date = fields.Datetime.today() - timedelta(days=days)
        end_date = fields.Datetime.today()

        sale_lines = self.env['sale.order.line'].search([
            ('order_id.date_order', '>=', start_date),
            ('order_id.date_order', '<=', end_date),
            ('product_id', '=', product.id),
            ('order_id.partner_id', '=', partner.id),
            ('order_id.state', 'in', ['sale', 'done']),
        ])

        if not sale_lines:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': '信息',
                    'message': f'未找到过去{days}天的历史销售记录',
                    'type': 'info',
                    'sticky': False,
                }
            }

        line_data = []
        for line in sale_lines:
            qty = line.product_uom_qty
            # 避免除零错误：当数量为0时，单价设为0
            if qty > 0:
                unit_price = line.price_subtotal / qty
            else:
                unit_price = 0.0
            line_data.append({
                'line': line,
                'unit_price': unit_price,
                'subtotal': line.price_subtotal,
                'qty': qty,
            })

        # 检查是否有有效的数据行
        if not line_data:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': '信息',
                    'message': f'过去{days}天的历史销售记录中没有有效的数量数据',
                    'type': 'info',
                    'sticky': False,
                }
            }

        # 计算总金额和总数量
        total_amount = sum(x['subtotal'] for x in line_data)
        total_qty = sum(x['qty'] for x in line_data)

        # 计算加权平均价：总金额 / 总数量
        avg_price = total_amount / total_qty if total_qty > 0 else 0.0

        max_item = max(line_data, key=lambda x: x['unit_price'])
        min_item = min(line_data, key=lambda x: x['unit_price'])

        wizard_vals = {
            'product_id': product.id,
            'max_price': max_item['unit_price'],
            'max_sale_line_id': max_item['line'].id,
            'min_price': min_item['unit_price'],
            'min_sale_line_id': min_item['line'].id,
            'avg_price': avg_price,
            'sale_order_line_id': self.id if self.id else False,
        }

        wizard = self.env['sale.order.history.price'].create(wizard_vals)

        return {
            'name': '查看历史价格',
            'type': 'ir.actions.act_window',
            'res_model': 'sale.order.history.price',
            'view_mode': 'list',
            'target': 'new',
            'domain': [('id', '=', wizard.id)],
        }

    @api.depends('product_id', 'order_id.pricelist_id', 'price_unit', 'product_uom_qty', 'product_uom',
                 'order_id.date_order')
    def _compute_suggested_price(self):
        """Compute the suggested price and check if product has price list item"""
        for line in self:
            if not line.product_id or not line.order_id.pricelist_id:
                line.suggested_price = 0.0
                line.has_pricelist_item = False
                continue

            # Check if there's a price list item for this product
            pricelist_item = self.env['product.pricelist.item'].search([
                ('pricelist_id', '=', line.order_id.pricelist_id.id),
                '|',
                ('product_tmpl_id', '=', line.product_id.product_tmpl_id.id),
                ('product_id', '=', line.product_id.id),
            ], limit=1)

            line.has_pricelist_item = bool(pricelist_item)

            # Get the price from the pricelist
            line.suggested_price = line.order_id.pricelist_id._get_product_price(
                line.product_id,
                line.product_uom_qty,
                uom=line.product_uom,
                date=line.order_id.date_order
            )

    @api.depends('suggested_price', 'price_unit')
    def _compute_price_difference(self):
        """Compute the price difference"""
        for line in self:
            if not line.suggested_price:
                line.price_difference = 0.0
                continue

            line.price_difference = line.price_unit - line.suggested_price

    @api.onchange('product_id', 'order_id.partner_id', 'price_unit')
    def _compute_show_price_sync_btn(self):
        """计算是否显示价格同步按钮"""
        for line in self:
            # 默认不显示按钮
            line.show_price_sync_btn = False
            line.price_sync_tooltip = ""

            # 检查基本条件
            if (not line.product_id or
                not line.order_id.partner_id or
                not line.price_unit):
                continue

            # 检查是否有私有价格表，如果没有则自动创建
            partner = line.order_id.partner_id
            try:
                private_pricelist = partner.get_private_pricelist(line.company_id)
                if not private_pricelist:
                    # 如果没有私有价格表，显示按钮以便创建
                    line.show_price_sync_btn = True
                    line.price_sync_tooltip = f"客户没有私有价格表，点击创建并添加价格({line.price_unit})"
                    continue

                # 检查产品是否在私有价格表中
                pricelist_item = self.env['product.pricelist.item'].search([
                    ('pricelist_id', '=', private_pricelist.id),
                    '|',
                    ('product_tmpl_id', '=', line.product_id.product_tmpl_id.id),
                    ('product_id', '=', line.product_id.id),
                ], limit=1)

                if pricelist_item:
                    # 产品在价格表中，检查价格是否不同
                    price_diff = abs(line.price_unit - pricelist_item.fixed_price)

                    if price_diff > 0.0001:  # 改为0.0001，避免浮点数精度问题
                        line.show_price_sync_btn = True
                        line.price_sync_tooltip = f"单据价格({line.price_unit})与价格表价格({pricelist_item.fixed_price})不一致，点击同步"
                    else:
                        line.show_price_sync_btn = False
                        line.price_sync_tooltip = ""
                else:
                    # 产品不在价格表中
                    line.show_price_sync_btn = True
                    line.price_sync_tooltip = f"产品不在私有价格表中，点击添加价格({line.price_unit})"

            except Exception:
                # 出错时也显示按钮，让用户可以尝试操作
                line.show_price_sync_btn = True
                line.price_sync_tooltip = f"检查价格表时出错，点击尝试同步价格({line.price_unit})"

    @api.onchange('product_id', 'product_uom_qty')
    def _onchange_product_id_update_suggested_price(self):
        """Update suggested price when product or quantity changes"""
        if self.product_id and self.order_id.pricelist_id:
            self._compute_suggested_price()

    def get_alternative_prices(self):
        """Get alternative prices from other price lists"""
        self.ensure_one()
        if not self.product_id:
            return []

        # Get all price lists except the current one
        other_pricelists = self.env['product.pricelist'].search([
            ('id', '!=', self.order_id.pricelist_id.id),
            '|',
            ('company_id', '=', False),
            ('company_id', '=', self.company_id.id),
        ])

        alternative_prices = []
        for pricelist in other_pricelists:
            # Check if product exists in this price list
            pricelist_item = self.env['product.pricelist.item'].search([
                ('pricelist_id', '=', pricelist.id),
                '|',
                ('product_tmpl_id', '=', self.product_id.product_tmpl_id.id),
                ('product_id', '=', self.product_id.id),
            ], limit=1)

            if pricelist_item:
                # Get the price from this pricelist
                price = pricelist._get_product_price(
                    self.product_id,
                    self.product_uom_qty,
                    uom=self.product_uom,
                    date=self.order_id.date_order
                )

                alternative_prices.append({
                    'id': pricelist.id,
                    'name': pricelist.name,
                    'price': price,
                    'is_global': not pricelist.company_id,
                })

        # Sort by global first, then by price list name
        alternative_prices.sort(key=lambda x: (not x['is_global'], x['name']))

        # Return only the first 5 results
        return alternative_prices[:5]

    def update_partner_pricelist_price(self, price):
        """将当前价格写入partner私有价格表：有则更新，无则新增"""
        self.ensure_one()
        partner = self.order_id.partner_id
        company = self.company_id

        # 获取客户私有价格表
        pricelist = partner.get_private_pricelist(company)
        if not pricelist:
            raise UserError(_("未找到客户的私有价格表"))

        product = self.product_id
        # 查找该产品的价格项（支持模板和变体两种）
        item = self.env['product.pricelist.item'].search([
            ('pricelist_id', '=', pricelist.id),
            '|',
            ('product_tmpl_id', '=', product.product_tmpl_id.id),
            ('product_id', '=', product.id),
        ], limit=1)

        if item:
            item.write({'fixed_price': price})
        else:
            self.env['product.pricelist.item'].create({
                'pricelist_id': pricelist.id,
                # 'applied_on': '0_product_variant',
                'product_id': product.id,
                'product_tmpl_id': product.product_tmpl_id.id,
                'compute_price': 'fixed',
                'fixed_price': price,
            })

        # 强制刷新缓存，确保新价格立即可用
        self.env.flush_all()
        self.env.invalidate_all()

        # 返回更新结果，前端可用
        return {
            'success': True,
            'price': price,
            'price_list_price': price,
            'updated': True
        }

    def get_partner_pricelist_price(self):
        """获取伙伴私有价格表价格"""
        self.ensure_one()
        partner = self.order_id.partner_id
        company = self.company_id
        product = self.product_id

        if not partner or not company or not product:
            return []

        pricelist = partner.get_private_pricelist(company)
        if not pricelist:
            return []

        # 获取价格表项
        item = self.env['product.pricelist.item'].search([
            ('pricelist_id', '=', pricelist.id),
            '|',
            ('product_tmpl_id', '=', product.product_tmpl_id.id),
            ('product_id', '=', product.id),
        ], limit=1)

        if not item:
            return []

        # 强制刷新缓存，确保获取到最新的价格
        self.env.flush_all()
        self.env.invalidate_all()

        return [{
            'id': self.id,
            'price': item.fixed_price,
            'timestamp': fields.Datetime.now(),  # 添加时间戳以便前端判断数据新鲜度
        }]

    @api.model
    def check_product_in_pricelist(self, partner_id, product_id):
        """
        代理调用res.partner的check_product_in_pricelist
        """
        return self.env['res.partner'].check_product_in_pricelist(partner_id, product_id)

    @api.model
    def add_product_to_pricelist(self, line_id, price):
        """
        代理调用res.partner的add_product_to_pricelist
        """
        line_obj = self.env['sale.order.line'].browse(line_id)
        pricelist_id = line_obj.order_id.pricelist_id.id
        product_id = line_obj.product_id
        item = self.env['product.pricelist.item'].create({
            'pricelist_id': pricelist_id,
            'product_id': product_id.id,
            'product_tmpl_id': product_id.product_tmpl_id.id,
            'price': price,
        })
        return item.id if item else False

    def action_sync_price_to_pricelist(self):
        """同步价格到私有价格表的按钮动作"""
        # 检查是否有 skip_save 上下文
        if self.env.context.get('skip_save'):
            # 如果有 skip_save，说明是未保存的记录，需要从上下文获取产品信息
            product_id = self.env.context.get('product_id')
            if not product_id:
                # 尝试从当前记录获取产品ID
                if hasattr(self, 'product_id') and self.product_id:
                    product_id = self.product_id.id
                else:
                    return {
                        'type': 'ir.actions.client',
                        'tag': 'display_notification',
                        'params': {
                            'title': '错误',
                            'message': '缺少产品信息',
                            'type': 'danger',
                            'sticky': False,
                        }
                    }
            
            # 使用产品ID创建临时记录集进行处理
            product = self.env['product.product'].browse(product_id)
            if not product.exists():
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': '错误',
                        'message': '无效的产品',
                        'type': 'danger',
                        'sticky': False,
                    }
                }
            
            # 处理未保存记录的价格同步逻辑
            return self._sync_price_for_product(product)
        else:
            # 原有逻辑：已保存的记录
            self.ensure_one()
            # 原有的价格同步逻辑
            return self._sync_price_for_existing_record()

    def _sync_price_for_product(self, product):
        """为指定产品同步价格（用于未保存的记录）"""
        # 从上下文获取必要信息
        if not self.id:
            # 未保存的记录，从上下文获取信息
            order_id = self.env.context.get('order_id')
            price_unit = self.env.context.get('price_unit')
            company_id = self.env.context.get('company_id', self.env.company.id)
            
            if not order_id:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': '错误',
                        'message': '缺少订单信息',
                        'type': 'danger',
                        'sticky': False,
                    }
                }
            
            order = self.env['sale.order'].browse(order_id)
            partner_id = order.partner_id.id
            
            if not price_unit:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': '错误',
                        'message': '缺少价格信息',
                        'type': 'danger',
                        'sticky': False,
                    }
                }
        else:
            # 已保存的记录，直接从self获取信息
            partner_id = self.order_id.partner_id.id
            price_unit = self.price_unit
            company_id = self.company_id.id
        
        try:
            partner = self.env['res.partner'].browse(partner_id)
            company = self.env['res.company'].browse(company_id)
            
            # 获取或创建客户的私有价格表
            private_pricelist = partner.get_private_pricelist(company)
            if not private_pricelist:
                private_pricelist = partner.ensure_private_pricelist(company)
                created_pricelist = True
            else:
                created_pricelist = False

            # 查找或创建价格表项
            pricelist_item = self.env['product.pricelist.item'].search([
                ('pricelist_id', '=', private_pricelist.id),
                '|',
                ('product_tmpl_id', '=', product.product_tmpl_id.id),
                ('product_id', '=', product.id),
            ], limit=1)

            if pricelist_item:
                # 更新现有价格表项
                old_price = pricelist_item.fixed_price
                pricelist_item.write({'fixed_price': price_unit})
                if created_pricelist:
                    message = f'已创建私有价格表并更新价格：{old_price} → {price_unit}'
                else:
                    message = f'已更新价格表中的价格：{old_price} → {price_unit}'
            else:
                # 创建新的价格表项
                self.env['product.pricelist.item'].create({
                    'pricelist_id': private_pricelist.id,
                    'product_id': product.id,
                    'product_tmpl_id': product.product_tmpl_id.id,
                    'compute_price': 'fixed',
                    'fixed_price': price_unit,
                })
                if created_pricelist:
                    message = f'已创建私有价格表并添加产品，价格：{price_unit}'
                else:
                    message = f'已添加产品到价格表，价格：{price_unit}'

            # 强制刷新缓存
            self.env.flush_all()
            self.env.invalidate_all()

            # 重新计算显示状态
            self._compute_show_price_sync_btn()

            return {
                'actions': [
                    {
                        'type': 'ir.actions.client',
                        'tag': 'display_notification',
                        'params': {
                            'title': '成功',
                            'message': message,
                            'type': 'success',
                            'sticky': False,
                        }
                    },
                    {
                        'type': 'ir.actions.client',
                        'tag': 'reload',
                    }
                ]
            }

        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"Error syncing price for product {product.id}: {str(e)}", exc_info=True)

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': '错误',
                    'message': f'同步价格失败：{str(e)}',
                    'type': 'danger',
                    'sticky': False,
                }
            }

    def _sync_price_for_existing_record(self):
        """为已保存的记录同步价格（原有逻辑）"""
        if not self.product_id or not self.price_unit:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': '错误',
                    'message': '缺少产品或价格信息',
                    'type': 'danger',
                    'sticky': False,
                }
            }

        partner = self.order_id.partner_id
        if not partner:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': '错误',
                    'message': '订单缺少客户信息',
                    'type': 'danger',
                    'sticky': False,
                }
            }

        try:
            # 获取或创建客户的私有价格表
            private_pricelist = partner.get_private_pricelist(self.company_id)
            if not private_pricelist:
                private_pricelist = partner.ensure_private_pricelist(self.company_id)
                created_pricelist = True
            else:
                created_pricelist = False

            # 查找或创建价格表项
            pricelist_item = self.env['product.pricelist.item'].search([
                ('pricelist_id', '=', private_pricelist.id),
                '|',
                ('product_tmpl_id', '=', self.product_id.product_tmpl_id.id),
                ('product_id', '=', self.product_id.id),
            ], limit=1)

            if pricelist_item:
                # 更新现有价格表项
                old_price = pricelist_item.fixed_price
                pricelist_item.write({'fixed_price': self.price_unit})
                if created_pricelist:
                    message = f'已创建私有价格表并更新价格：{old_price} → {self.price_unit}'
                else:
                    message = f'已更新价格表中的价格：{old_price} → {self.price_unit}'
            else:
                # 创建新的价格表项
                self.env['product.pricelist.item'].create({
                    'pricelist_id': private_pricelist.id,
                    'product_id': self.product_id.id,
                    'product_tmpl_id': self.product_id.product_tmpl_id.id,
                    'compute_price': 'fixed',
                    'fixed_price': self.price_unit,
                })
                if created_pricelist:
                    message = f'已创建私有价格表并添加产品，价格：{self.price_unit}'
                else:
                    message = f'已添加产品到价格表，价格：{self.price_unit}'

            # 强制刷新缓存和重新计算
            self.env.flush_all()
            self.env.invalidate_all()

            # 重新计算显示状态
            self._compute_show_price_sync_btn()

            return {
                'type': 'ir.actions.act_multi',
                'actions': [
                    {
                        'type': 'ir.actions.client',
                        'tag': 'display_notification',
                        'params': {
                            'title': '成功',
                            'message': message,
                            'type': 'success',
                            'sticky': False,
                        }
                    },
                    {
                        'type': 'ir.actions.client',
                        'tag': 'reload',
                    }
                ]
            }

        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"Error syncing price to pricelist: {str(e)}", exc_info=True)

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': '错误',
                    'message': f'同步价格失败：{str(e)}',
                    'type': 'danger',
                    'sticky': False,
                }
            }

    @api.model
    def action_open_no_pricelist_lines(self):
        """
        动态创建向导记录，并打开list视图
        """
        self.env['sale.order.line.no.pricelist'].search([]).unlink()

        # 查找所有没有价格表的销售订单行
        sale_lines = self.env['sale.order.line'].search([('product_id.type', '!=', 'service')])
        for line in sale_lines:
            if line.product_id.pricelist_item_count == 0:
                self.env['sale.order.line.no.pricelist'].create({
                    'product_id': line.product_id.id,
                    'partner_id': line.order_id.partner_id.id,
                    'product_uom_qty': line.product_uom_qty,
                    'price_unit': line.price_unit,
                    'price_subtotal': line.price_subtotal,
                    'date_order': line.order_id.date_order,
                    'price_untaxed': round((line.price_subtotal / line.product_uom_qty), 4) if line.product_uom_qty else 0.0
                })

        # 打开list视图
        return {
            'type': 'ir.actions.act_window',
            'name': '价格表维护',
            'res_model': 'sale.order.line.no.pricelist',
            'view_mode': 'list',
            'target': 'current',
        }
