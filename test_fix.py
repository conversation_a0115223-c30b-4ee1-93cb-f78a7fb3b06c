#!/usr/bin/env python3
"""
测试脚本：验证采购预付款剩余金额计算修复
"""

def test_residual_calculation_logic():
    """
    测试剩余金额计算逻辑
    模拟两个采购订单在同一张付款单的情况
    """
    
    # 模拟采购订单1的数据
    order1_total = 163.85  # 采购订单1总金额
    order1_advance = 163.85  # 预付款金额
    order1_invoice_residual = 0.0  # 发票剩余金额（已支付）
    
    # 模拟采购订单2的数据  
    order2_total = 5559.60  # 采购订单2总金额
    order2_advance = 5559.60  # 预付款金额
    order2_invoice_residual = 0.0  # 发票剩余金额（已支付）
    
    print("=== 修复前的逻辑（有bug）===")
    # 旧逻辑：amount_residual = order.amount_total - advance_amount - invoice_paid_amount
    # 其中 invoice_paid_amount = inv.amount_total - inv.amount_residual
    
    # 采购订单1
    old_invoice_paid_1 = order1_total - order1_invoice_residual  # 163.85 - 0 = 163.85
    old_residual_1 = order1_total - order1_advance - old_invoice_paid_1  # 163.85 - 163.85 - 163.85 = -163.85
    print(f"采购订单1 - 旧逻辑剩余金额: {old_residual_1}")
    
    # 采购订单2  
    old_invoice_paid_2 = order2_total - order2_invoice_residual  # 5559.60 - 0 = 5559.60
    old_residual_2 = order2_total - order2_advance - old_invoice_paid_2  # 5559.60 - 5559.60 - 5559.60 = -5559.60
    print(f"采购订单2 - 旧逻辑剩余金额: {old_residual_2}")
    
    print("\n=== 修复后的逻辑（已修复）===")
    # 新逻辑：当有发票时，amount_residual = invoice_residual_amount + advance_amount
    # 其中 advance_amount 只计算未核销的预付款
    
    # 假设预付款已经与发票核销，所以 advance_amount = 0
    unreconciled_advance_1 = 0.0  # 预付款已核销
    unreconciled_advance_2 = 0.0  # 预付款已核销
    
    # 采购订单1
    new_residual_1 = order1_invoice_residual + unreconciled_advance_1  # 0 + 0 = 0
    print(f"采购订单1 - 新逻辑剩余金额: {new_residual_1}")
    
    # 采购订单2
    new_residual_2 = order2_invoice_residual + unreconciled_advance_2  # 0 + 0 = 0  
    print(f"采购订单2 - 新逻辑剩余金额: {new_residual_2}")
    
    print("\n=== 测试结果 ===")
    print("修复前：出现负数剩余金额（bug）")
    print("修复后：剩余金额为0（正确）")
    
    # 验证修复是否正确
    assert new_residual_1 >= 0, "采购订单1剩余金额不应为负数"
    assert new_residual_2 >= 0, "采购订单2剩余金额不应为负数"
    print("✅ 所有测试通过！")

if __name__ == "__main__":
    test_residual_calculation_logic()
